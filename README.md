# UniApp跨端项目

基于 UniApp + Vue3 + TypeScript + Vant 的跨端项目框架，支持H5和小程序双端运行。

## 🎯 技术栈

- **开发框架**: UniApp (CLI模式)
- **前端框架**: Vue3 (Composition API)
- **类型系统**: TypeScript 4.9+
- **UI组件库**: Vant 4.x (H5) + uni-ui (小程序)
- **状态管理**: Pinia 2.x
- **构建工具**: Vite 4.x
- **代码规范**: ESLint + Prettier
- **包管理器**: pnpm

## 📁 项目结构

```
├── src/                          # 源码目录
│   ├── components/               # 组件库
│   │   ├── common/              # 通用组件
│   │   ├── adapters/            # 平台适配组件
│   │   ├── h5/                  # H5专用组件
│   │   └── mp/                  # 小程序专用组件
│   ├── pages/                   # 页面目录
│   │   ├── index/               # 首页
│   │   ├── user/                # 用户相关页面
│   │   └── common/              # 公共页面
│   ├── api/                     # 接口管理
│   │   ├── modules/             # 接口模块
│   │   ├── types/               # 接口类型定义
│   │   └── request.ts           # 请求封装
│   ├── store/                   # 状态管理
│   │   ├── modules/             # store模块
│   │   └── index.ts             # store入口
│   ├── utils/                   # 工具函数
│   │   ├── platform.ts          # 平台判断
│   │   ├── storage.ts           # 存储封装
│   │   └── common.ts            # 通用工具
│   ├── types/                   # 全局类型定义
│   ├── styles/                  # 样式文件
│   │   ├── variables.scss       # 变量定义
│   │   ├── mixins.scss          # 混入
│   │   └── common.scss          # 通用样式
│   ├── static/                  # 静态资源
│   └── platforms/               # 平台特定配置
│       ├── h5/                  # H5平台配置
│       └── mp-weixin/           # 微信小程序配置
├── types/                       # 类型声明文件
├── pages.json                   # 页面配置
├── manifest.json                # 应用配置
├── uni.scss                     # 全局样式变量
├── vite.config.ts               # Vite配置
├── tsconfig.json                # TypeScript配置
└── package.json                 # 项目配置
```

## 🚀 快速开始

### 安装依赖

```bash
# 使用pnpm安装依赖
pnpm install
```

### 开发命令

```bash
# H5开发模式
pnpm dev:h5

# 小程序开发模式
pnpm dev:mp

# H5生产构建
pnpm build:h5

# 小程序生产构建
pnpm build:mp

# TypeScript类型检查
pnpm type-check

# 代码规范检查
pnpm lint

# 自动修复代码规范
pnpm lint:fix
```

## 🎨 核心特性

### 1. 跨平台适配

- **组件适配器**: 统一H5和小程序的组件API
- **平台判断**: 运行时平台检测和条件编译
- **样式适配**: 统一的设计令牌系统和平台特定样式

### 2. 状态管理

- **Pinia**: 现代化的状态管理库
- **持久化**: 跨平台的数据持久化
- **类型安全**: 完整的TypeScript支持

### 3. 网络请求

- **请求适配器**: 统一H5和小程序的网络请求
- **拦截器**: 请求和响应拦截处理
- **错误处理**: 统一的错误处理机制

### 4. 工具函数

- **平台工具**: 平台判断、系统信息、UI交互
- **存储工具**: 跨平台存储，支持过期时间
- **通用工具**: 常用的工具方法和辅助函数

## 📱 页面配置

### TabBar配置

- 首页: `/pages/index/index`
- 我的: `/pages/user/profile`

### 路由配置

- 登录页: `/pages/user/login`
- 个人中心: `/pages/user/profile`

## 🛠️ 开发规范

### 代码风格

- 使用ESLint + Prettier进行代码规范
- 统一的代码格式化配置
- TypeScript严格模式

### 组件开发

- 使用Composition API
- 组件名使用PascalCase
- Props使用TypeScript接口定义
- 使用scoped样式避免污染

### 样式规范

- 使用SCSS预处理器
- 统一的设计令牌系统
- 响应式设计支持
- 平台适配混入

## 🔧 环境配置

### 开发环境

- API地址: `http://localhost:3001/api`
- 调试模式: 开启
- Mock数据: 开启

### 生产环境

- API地址: `https://api.example.com/api`
- 调试模式: 关闭
- Mock数据: 关闭

## 📋 最佳实践

1. **理解优先**: 确保完全理解需求再开始编码
2. **简单有效**: 选择最直接的解决方案
3. **注重质量**: 代码可读性和可维护性优于复杂技巧
4. **主动沟通**: 遇到疑问立即询问，避免假设
5. **持续优化**: 根据反馈不断改进解决方案

## 📄 许可证

MIT License

---

**项目版本**: v1.0.0  
**创建时间**: 2025-01-18  
**适用范围**: UniApp + Vue3 + TypeScript + Vant 跨端项目
